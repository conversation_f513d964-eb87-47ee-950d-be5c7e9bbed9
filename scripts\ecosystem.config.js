module.exports = {
  apps: [{
    name: 'nextjs-excel-app',
    script: 'pnpm',
    args: 'run start',
    instances: 'max', // 使用所有可用CPU核心
    exec_mode: 'cluster',
    env: {
      NODE_ENV: 'production',
      PORT: 3000
    },
    env_production: {
      NODE_ENV: 'production',
      PORT: 3000
    },
    
    // 性能优化配置
    max_memory_restart: '1G',
    node_args: '--max-old-space-size=1024',
    
    // 日志配置
    log_file: './logs/combined.log',
    out_file: './logs/out.log',
    error_file: './logs/error.log',
    log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
    merge_logs: true,
    
    // 自动重启配置
    watch: false,
    ignore_watch: ['node_modules', 'logs', '.git'],
    
    // 健康检查和重启策略
    min_uptime: '10s',
    max_restarts: 10,
    restart_delay: 4000,
    
    // 优雅关闭
    kill_timeout: 5000,
    wait_ready: true,
    listen_timeout: 8000,
    
    // 自动重启条件
    autorestart: true,
    
    // 环境变量
    env_file: '.env.production.local'
  }]
}
