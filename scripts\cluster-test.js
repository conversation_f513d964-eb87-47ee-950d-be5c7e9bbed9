const http = require('http');
const process = require('process');

// 配置参数 - 根据实际情况修改
const config = {
  apiUrl: 'http://dapingguo.xyz/api/auth-check',
  apiSecret: 'AKIDJXhnkcWK0kQ8jBgO2aw523yjHbhaHsHY',
  testToken: 'eyJhbGciOiJkaXIiLCJlbmMiOiJBMjU2R0NNIn0..Alj6USQdQ8GJFoUh.Cy8c7OaXObL-F1BED7xfS0DPiJWrIld-y1GnO1RbN2OStBtKVX9_8MSyRdvfblrI0A_wx21yno5eK46Iu6ZGBQlki_uh6S6nJY0IKpdDHXgw6etTkEV_1rZFyke3b2ggmm7IxKxwY7nqDblVcpf3prKlTjLLk4Rm9agkR58aTRrwn7qkgsDNUpgVScVsIBXqgnLHztC3LP3GdSnPrbBith6gkkyouhtfe-CsZsQQ3sqM6BBIiZ3VhUJV7g_G9k00n7yNmcLZ7A.JxRbSe1x04fancZJfyj_3Q',
  concurrency: 10,
  testInterval: 30000
};

async function runClusterTest() {
  const results = [];
  const requests = [];
  
  for (let i = 0; i < config.concurrency; i++) {
    requests.push(new Promise((resolve) => {
      const startTime = Date.now();
      const reqId = i + 1;
      
      http.get(`${config.apiUrl}?req=${reqId}`, {
        headers: {
          'Cookie': `token=${config.testToken};`, // 修复：添加分号
          'x-api-secret': config.apiSecret
        }
      }, (res) => {
        let data = '';
        res.on('data', chunk => data += chunk);
        res.on('end', () => {
          try {
            const response = JSON.parse(data);
            results.push({
              requestId: reqId,
              serverPort: Number(response.serverPort),
              serverId: response.serverId,
              isAuthenticated: response.isAuthenticated,
              responseTime: Date.now() - startTime,
              statusCode: res.statusCode || 200
            });
          } catch (e) {
            results.push({
              requestId: reqId,
              serverPort: 0,
              serverId: 0,
              isAuthenticated: false,
              responseTime: Date.now() - startTime,
              statusCode: 500
            });
          }
          resolve();
        });
      }).on('error', () => {
        results.push({
          requestId: reqId,
          serverPort: 0,
          serverId: 0,
          isAuthenticated: false,
          responseTime: Date.now() - startTime,
          statusCode: 500
        });
        resolve();
      });
    }));
  }
  
  await Promise.all(requests);
  return results;
}

function printResults(results) {
  console.log('\n=== Cluster Test Results ===');
  
  results.forEach(r => {
    console.log(`[Req ${r.requestId}] Status: ${r.statusCode} | Server: ${r.serverPort} (PID: ${r.serverId}) | Auth: ${r.isAuthenticated} | Time: ${r.responseTime}ms`);
  });
  
  const successRequests = results.filter(r => r.statusCode === 200);
  const uniquePorts = new Set(successRequests.map(r => r.serverPort));
  const uniquePids = new Set(successRequests.map(r => r.serverId));
  const authSuccessRate = successRequests.filter(r => r.isAuthenticated).length / successRequests.length * 100;
  const avgTime = successRequests.reduce((sum, r) => sum + r.responseTime, 0) / successRequests.length;
  
  console.log('\n--- Statistics ---');
  console.log(`Total requests: ${results.length}`);
  console.log(`Success rate: ${(successRequests.length / results.length * 100).toFixed(1)}%`);
  console.log(`Unique ports: ${uniquePorts.size}`);
  console.log(`Unique PIDs: ${uniquePids.size}`);
  console.log(`Authentication success rate: ${authSuccessRate.toFixed(1)}%`);
  console.log(`Average response time: ${avgTime.toFixed(1)}ms`);
}

setInterval(async () => {
  console.log(`\n[${new Date().toISOString()}] Starting cluster test...`);
  const results = await runClusterTest();
  printResults(results);
}, config.testInterval);

// 立即运行第一次测试
(async () => {
  console.log('Running initial cluster test...');
  const results = await runClusterTest();
  printResults(results);
  console.log(`Next test in ${config.testInterval / 1000} seconds...`);
})();