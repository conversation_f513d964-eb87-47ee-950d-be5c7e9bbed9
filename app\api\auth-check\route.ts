// app/api/auth-check/route.ts
import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'

// 定义响应数据类型
export interface AuthCheckResponse {
  serverPort: string | number;
  serverId: number;
  serverTime: string;
  isAuthenticated: boolean;
  tokenExists: boolean;
  tokenValid: boolean;
  requestHeaders: Record<string, string>;
}

// 可选：添加环境变量验证
const API_SECRET = process.env.API_SECRET || 'default-secret'

export async function GET(request: NextRequest) {
  try {
    // 验证请求来源（可选安全措施）
    const clientSecret = request.headers.get('x-api-secret')
    if (clientSecret !== API_SECRET) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    // 创建响应对象
    const responseData: AuthCheckResponse = {
      serverPort: process.env.PORT || 3000,
      serverId: process.pid,
      serverTime: new Date().toISOString(),
      isAuthenticated: false,
      tokenExists: false,
      tokenValid: false,
      requestHeaders: Object.fromEntries(request.headers.entries())
    }

    // 检查认证状态
    const token = request.cookies.get('token')?.value
    
    if (token) {
      responseData.tokenExists = true
      responseData.isAuthenticated = true
      
      // 简单验证 token 格式
      if (token.length > 30) {
        responseData.tokenValid = true
      }
    }

    return NextResponse.json(responseData, {
      status: 200,
      headers: {
        'Cache-Control': 'no-store, max-age=0'
      }
    })
    
  } catch (error) {
    return NextResponse.json(
      { error: 'Internal Server Error' },
      { status: 500 }
    )
  }
}