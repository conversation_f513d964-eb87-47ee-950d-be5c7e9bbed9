// app/api/auth-check/route.ts
import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'
import { getServerSession } from 'next-auth/next'
import { authOptions } from '@/app/lib/auth'

// 定义响应数据类型
export interface AuthCheckResponse {
  serverPort: string | number;
  serverId: number;
  serverTime: string;
  isAuthenticated: boolean;
  tokenExists: boolean;
  tokenValid: boolean;
  sessionData?: any;
  cookieInfo: {
    nextAuthSessionToken?: string;
    nextAuthCsrfToken?: string;
    allCookies: string[];
  };
  requestHeaders: Record<string, string>;
}

// 可选：添加环境变量验证
const API_SECRET = process.env.API_SECRET || 'default-secret'

export async function GET(request: NextRequest) {
  try {
    // 验证请求来源（可选安全措施）
    const clientSecret = request.headers.get('x-api-secret')
    if (clientSecret !== API_SECRET) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    // 获取NextAuth session
    const session = await getServerSession(authOptions)

    // 获取所有cookies
    const allCookies = Array.from(request.cookies.getAll()).map(cookie =>
      `${cookie.name}=${cookie.value.substring(0, 20)}...`
    )

    // 创建响应对象
    const responseData: AuthCheckResponse = {
      serverPort: process.env.PORT || 3000,
      serverId: process.pid,
      serverTime: new Date().toISOString(),
      isAuthenticated: !!session,
      tokenExists: false,
      tokenValid: false,
      sessionData: session ? {
        userId: session.user.id,
        username: session.user.username,
        email: session.user.email,
        score: session.user.score,
        level: session.user.level,
        userType: session.user.userType
      } : null,
      cookieInfo: {
        nextAuthSessionToken: request.cookies.get('next-auth.session-token')?.value?.substring(0, 50) + '...',
        nextAuthCsrfToken: request.cookies.get('next-auth.csrf-token')?.value?.substring(0, 50) + '...',
        allCookies
      },
      requestHeaders: Object.fromEntries(request.headers.entries())
    }

    // 检查NextAuth JWT token
    const sessionToken = request.cookies.get('next-auth.session-token')?.value

    if (sessionToken) {
      responseData.tokenExists = true

      // 简单验证 JWT token 格式 (应该有3个部分，用.分隔)
      const tokenParts = sessionToken.split('.')
      if (tokenParts.length === 3) {
        responseData.tokenValid = true
      }
    }

    return NextResponse.json(responseData, {
      status: 200,
      headers: {
        'Cache-Control': 'no-store, max-age=0'
      }
    })

  } catch (error) {
    console.error('Auth check error:', error)
    return NextResponse.json(
      { error: 'Internal Server Error', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    )
  }
}