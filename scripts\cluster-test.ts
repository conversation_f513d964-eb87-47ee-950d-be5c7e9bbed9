import http from 'http';
import process from 'process';

// 配置参数 - 根据实际情况修改
const config = {
  apiUrl: 'http://dapingguo.xyz/api/auth-check',
  apiSecret: 'AKIDJXhnkcWK0kQ8jBgO2aw523yjHbhaHsHY', // 与 Nginx 配置一致
  testToken: '72c2795420051a83e4d53a13ef0fe1810962905e8a386754d1f0792bf54ccafa%7C9514fc4c492dc892138163221792b5a281024d021865c49fd6131bd475f34f7a', // 登录后获取的实际 token
  concurrency: 10,
  testInterval: 30000
};

interface TestResult {
  requestId: number;
  serverPort: number;
  serverId: number;
  isAuthenticated: boolean;
  responseTime: number;
  statusCode: number;
}

async function runClusterTest(): Promise<TestResult[]> {
  const results: TestResult[] = [];
  const requests: Promise<void>[] = [];
  
  for (let i = 0; i < config.concurrency; i++) {
    requests.push(new Promise((resolve) => {
      const startTime = Date.now();
      const reqId = i + 1;
      
      http.get(`${config.apiUrl}?req=${reqId}`, {
        headers: {
          'Cookie': `token=${config.testToken}`,
          'x-api-secret': config.apiSecret
        }
      }, (res) => {
        let data = '';
        res.on('data', chunk => data += chunk);
        res.on('end', () => {
          try {
            const response = JSON.parse(data);
            results.push({
              requestId: reqId,
              serverPort: Number(response.serverPort),
              serverId: response.serverId,
              isAuthenticated: response.isAuthenticated,
              responseTime: Date.now() - startTime,
              statusCode: res.statusCode || 200
            });
          } catch (e) {
            results.push({
              requestId: reqId,
              serverPort: 0,
              serverId: 0,
              isAuthenticated: false,
              responseTime: Date.now() - startTime,
              statusCode: 500
            });
          }
          resolve();
        });
      }).on('error', (e) => {
        results.push({
          requestId: reqId,
          serverPort: 0,
          serverId: 0,
          isAuthenticated: false,
          responseTime: Date.now() - startTime,
          statusCode: 500
        });
        resolve();
      });
    }));
  }
  
  await Promise.all(requests);
  return results;
}

function printResults(results: TestResult[]) {
  console.log('\n=== Cluster Test Results ===');
  
  // 打印每个请求结果
  results.forEach(r => {
    console.log(`[Req ${r.requestId}] Status: ${r.statusCode} | Server: ${r.serverPort} (PID: ${r.serverId}) | Auth: ${r.isAuthenticated} | Time: ${r.responseTime}ms`);
  });
  
  // 统计信息
  const successRequests = results.filter(r => r.statusCode === 200);
  const uniquePorts = new Set(successRequests.map(r => r.serverPort));
  const uniquePids = new Set(successRequests.map(r => r.serverId));
  const authSuccessRate = successRequests.filter(r => r.isAuthenticated).length / successRequests.length * 100;
  const avgTime = successRequests.reduce((sum, r) => sum + r.responseTime, 0) / successRequests.length;
  
  console.log('\n--- Statistics ---');
  console.log(`Total requests: ${results.length}`);
  console.log(`Success rate: ${(successRequests.length / results.length * 100).toFixed(1)}%`);
  console.log(`Unique ports: ${uniquePorts.size}`);
  console.log(`Unique PIDs: ${uniquePids.size}`);
  console.log(`Authentication success rate: ${authSuccessRate.toFixed(1)}%`);
  console.log(`Average response time: ${avgTime.toFixed(1)}ms`);
}

// 定时运行测试
setInterval(async () => {
  console.log(`\n[${new Date().toISOString()}] Starting cluster test...`);
  const results = await runClusterTest();
  printResults(results);
}, config.testInterval);

// 立即运行第一次测试
(async () => {
  console.log('Running initial cluster test...');
  const results = await runClusterTest();
  printResults(results);
  console.log(`Next test in ${config.testInterval / 1000} seconds...`);
})();