﻿
# 登录参数
$baseUrl = "https://dapingguo.xyz"
$email = "<EMAIL>"
$password = "123456"

# NextAuth.js 的典型登录端点
$loginUrl = "$baseUrl/api/auth/callback/credentials"

try {
    # 执行登录 - 使用表单格式发送
    $loginResponse = Invoke-WebRequest `
        -Uri $loginUrl `
        -Method POST `
        -Body @{
            email = $email
            password = $password
            redirect = "false" # 防止重定向
            callbackUrl = "$baseUrl/dashboard" # NextAuth 需要此参数
        } `
        -SessionVariable session `
        -ErrorAction Stop

    # 检查响应状态
    if ($loginResponse.StatusCode -ne 200) {
        Write-Host "❌ 登录失败，状态码: $($loginResponse.StatusCode)" -ForegroundColor Red
        Write-Host "响应内容: $($loginResponse.Content)" -ForegroundColor Yellow
        exit
    }

    # 提取 session token - NextAuth 默认 cookie 名称
    $sessionCookie = $session.Cookies.GetCookies($baseUrl) | 
        Where-Object { $_.Name -eq "next-auth.session-token" }
    
    if ($sessionCookie) {
        $SESSION_TOKEN = $sessionCookie.Value
        Write-Host "✅ 登录成功! Session Token: $SESSION_TOKEN" -ForegroundColor Green
    }
    else {
        Write-Host "❌ 未找到 session token cookie" -ForegroundColor Red
        
        # 输出所有 cookie 用于调试
        Write-Host "找到的 Cookie:"
        $session.Cookies.GetCookies($baseUrl) | 
            ForEach-Object { 
                Write-Host "  - $($_.Name): $($_.Value)" 
            }
    }
}
catch {
    Write-Host "❌ 登录请求失败: $_" -ForegroundColor Red
    if ($_.Exception.Response) {
        $statusCode = $_.Exception.Response.StatusCode.Value__
        $responseStream = $_.Exception.Response.GetResponseStream()
        $reader = New-Object System.IO.StreamReader($responseStream)
        $errorResponse = $reader.ReadToEnd()
        Write-Host "状态码: $statusCode"
        Write-Host "错误响应: $errorResponse"
    }
}