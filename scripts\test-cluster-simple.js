#!/usr/bin/env node

/**
 * 简化版PM2集群测试脚本
 * 
 * 使用方法：
 * 1. 在浏览器中登录您的应用
 * 2. 打开开发者工具 -> Application -> Cookies
 * 3. 复制 next-auth.session-token 的值
 * 4. 运行: node scripts/test-cluster-simple.js "your-session-token-here"
 */

const https = require('https');
const http = require('http');

// 配置
const CONFIG = {
  baseUrl: 'https://dapingguo.xyz',
  apiSecret: process.env.API_SECRET || 'AKIDJXhnkcWK0kQ8jBgO2aw523yjHbhaHsHY',
  requestCount: 20,
  requestInterval: 500
};

// 颜色输出
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

// HTTP请求函数
function makeRequest(url, options = {}) {
  return new Promise((resolve, reject) => {
    const isHttps = url.startsWith('https');
    const client = isHttps ? https : http;
    
    const req = client.request(url, options, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        try {
          const jsonData = JSON.parse(data);
          resolve({
            statusCode: res.statusCode,
            headers: res.headers,
            data: jsonData
          });
        } catch (e) {
          resolve({
            statusCode: res.statusCode,
            headers: res.headers,
            data: data
          });
        }
      });
    });
    
    req.on('error', reject);
    
    if (options.body) {
      req.write(options.body);
    }
    
    req.end();
  });
}

// 测试auth-check API
async function testAuthCheck(sessionToken, requestIndex) {
  const url = `${CONFIG.baseUrl}/api/auth-check`;
  
  try {
    const response = await makeRequest(url, {
      method: 'GET',
      headers: {
        'x-api-secret': CONFIG.apiSecret,
        'Cookie': `next-auth.session-token=${sessionToken}`,
        'User-Agent': `ClusterTest-${requestIndex}`
      }
    });

    if (response.statusCode === 200) {
      return response.data;
    } else {
      log(`❌ 请求 ${requestIndex} 失败: ${response.statusCode}`, 'red');
      if (response.statusCode === 401) {
        log('可能是API_SECRET不正确或session token无效', 'yellow');
      }
      return null;
    }
  } catch (error) {
    log(`❌ 请求 ${requestIndex} 错误: ${error.message}`, 'red');
    return null;
  }
}

// 分析测试结果
function analyzeResults(results) {
  log('\n📊 测试结果分析:', 'cyan');
  log('='.repeat(50), 'cyan');

  const serverIds = new Set();
  const serverPorts = new Set();
  let authenticatedCount = 0;
  let validTokenCount = 0;

  results.forEach((result, index) => {
    if (result) {
      serverIds.add(result.serverId);
      serverPorts.add(result.serverPort);
      if (result.isAuthenticated) authenticatedCount++;
      if (result.tokenValid) validTokenCount++;
      
      log(`请求 ${index + 1}: 进程ID=${result.serverId}, 端口=${result.serverPort}, 认证=${result.isAuthenticated ? '✅' : '❌'}`, 
          result.isAuthenticated ? 'green' : 'red');
    }
  });

  log('\n🎯 汇总统计:', 'magenta');
  log(`总请求数: ${results.length}`, 'blue');
  log(`成功请求数: ${results.filter(r => r).length}`, 'green');
  log(`不同进程数: ${serverIds.size}`, serverIds.size > 1 ? 'green' : 'yellow');
  log(`不同端口数: ${serverPorts.size}`, 'blue');
  log(`认证成功数: ${authenticatedCount}`, authenticatedCount === results.filter(r => r).length ? 'green' : 'red');
  log(`Token有效数: ${validTokenCount}`, validTokenCount === results.filter(r => r).length ? 'green' : 'red');

  log('\n🔍 进程分布:', 'cyan');
  Array.from(serverIds).forEach(id => {
    const count = results.filter(r => r && r.serverId === id).length;
    log(`进程 ${id}: ${count} 次请求`, 'blue');
  });

  // 显示第一个成功请求的详细信息
  const firstSuccess = results.find(r => r && r.isAuthenticated);
  if (firstSuccess) {
    log('\n🔍 Session详情 (第一个成功请求):', 'cyan');
    if (firstSuccess.sessionData) {
      log(`用户ID: ${firstSuccess.sessionData.userId}`, 'blue');
      log(`用户名: ${firstSuccess.sessionData.username}`, 'blue');
      log(`邮箱: ${firstSuccess.sessionData.email}`, 'blue');
      log(`分数: ${firstSuccess.sessionData.score}`, 'blue');
      log(`等级: ${firstSuccess.sessionData.level}`, 'blue');
      log(`用户类型: ${firstSuccess.sessionData.userType}`, 'blue');
    }
  }

  // 判断测试结果
  log('\n🏆 测试结论:', 'magenta');
  if (serverIds.size > 1) {
    log('✅ 应用确实运行在多个进程上', 'green');
  } else {
    log('⚠️  应用只运行在单个进程上', 'yellow');
  }

  if (authenticatedCount === results.filter(r => r).length && authenticatedCount > 0) {
    log('✅ Session/Cookie在所有进程间正常工作', 'green');
  } else {
    log('❌ Session/Cookie存在问题', 'red');
  }

  if (serverIds.size > 1 && authenticatedCount === results.filter(r => r).length) {
    log('🎉 集群模式测试通过！', 'green');
  } else {
    log('⚠️  集群模式可能存在问题', 'yellow');
  }
}

// 主测试函数
async function runTest(sessionToken) {
  if (!sessionToken) {
    log('❌ 请提供session token', 'red');
    log('\n使用方法:', 'yellow');
    log('1. 在浏览器中登录您的应用', 'blue');
    log('2. 打开开发者工具 -> Application -> Cookies', 'blue');
    log('3. 复制 next-auth.session-token 的值', 'blue');
    log('4. 运行: node scripts/test-cluster-simple.js "your-session-token-here"', 'blue');
    return;
  }

  log('🚀 开始PM2集群模式和Session/Cookie测试', 'cyan');
  log(`测试目标: ${CONFIG.baseUrl}`, 'blue');
  log(`请求次数: ${CONFIG.requestCount}`, 'blue');
  log(`请求间隔: ${CONFIG.requestInterval}ms`, 'blue');
  log(`Session Token前50字符: ${sessionToken.substring(0, 50)}...`, 'yellow');

  // 发送多个请求测试
  log('\n🔄 开始发送测试请求...', 'blue');
  const results = [];

  for (let i = 0; i < CONFIG.requestCount; i++) {
    log(`发送请求 ${i + 1}/${CONFIG.requestCount}...`, 'yellow');
    const result = await testAuthCheck(sessionToken, i + 1);
    results.push(result);
    
    // 等待间隔
    if (i < CONFIG.requestCount - 1) {
      await new Promise(resolve => setTimeout(resolve, CONFIG.requestInterval));
    }
  }

  // 分析结果
  analyzeResults(results);
}

// 运行测试
if (require.main === module) {
  const sessionToken = process.argv[2];
  runTest(sessionToken).catch(error => {
    log(`❌ 测试失败: ${error.message}`, 'red');
    console.error(error);
    process.exit(1);
  });
}

module.exports = { runTest };
