# PM2集群模式和Session/Cookie测试脚本 (PowerShell版本)

param(
    [string]$BaseUrl = "https://dapingguo.xyz",
    [string]$TestEmail = "<EMAIL>",
    [string]$TestPassword = "123456",
    [int]$RequestCount = 20,
    [int]$RequestInterval = 500
)

# 配置
$API_SECRET = $env:API_SECRET
if (-not $API_SECRET) {
    $API_SECRET = "AKIDJXhnkcWK0kQ8jBgO2aw523yjHbhaHsHY"
}

Write-Host "🚀 开始PM2集群模式和Session/Cookie测试" -ForegroundColor Cyan
Write-Host "测试目标: $BaseUrl" -ForegroundColor Blue
Write-Host "请求次数: $RequestCount" -ForegroundColor Blue
Write-Host "请求间隔: ${RequestInterval}ms" -ForegroundColor Blue

# 登录获取session token
Write-Host "`n🔐 正在登录获取session token..." -ForegroundColor Blue

try {
    $loginUrl = "$BaseUrl/api/auth/signin"
    $loginBody = @{
        email = $TestEmail
        password = $TestPassword
        redirect = "false"
        callbackUrl = "$BaseUrl/dashboard"
    }

    $loginResponse = Invoke-WebRequest `
        -Uri $loginUrl `
        -Method POST `
        -Body $loginBody `
        -SessionVariable session `
        -ErrorAction Stop

    # 提取session cookie
    $sessionCookie = $session.Cookies.GetCookies($BaseUrl) | 
        Where-Object { $_.Name -eq "next-auth.session-token" }
    
    if ($sessionCookie) {
        $SESSION_TOKEN = $sessionCookie.Value
        Write-Host "✅ 登录成功，获取到session token" -ForegroundColor Green
        Write-Host "Token前50字符: $($SESSION_TOKEN.Substring(0, [Math]::Min(50, $SESSION_TOKEN.Length)))..." -ForegroundColor Yellow
    } else {
        Write-Host "❌ 登录失败，未获取到session token" -ForegroundColor Red
        Write-Host "响应状态: $($loginResponse.StatusCode)" -ForegroundColor Yellow
        exit 1
    }
} catch {
    Write-Host "❌ 登录请求失败: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# 发送测试请求
Write-Host "`n🔄 开始发送测试请求..." -ForegroundColor Blue

$results = @()
$serverIds = @{}
$serverPorts = @{}
$authenticatedCount = 0
$validTokenCount = 0

for ($i = 1; $i -le $RequestCount; $i++) {
    Write-Host "发送请求 $i/$RequestCount..." -ForegroundColor Yellow
    
    try {
        $authCheckUrl = "$BaseUrl/api/auth-check"
        $headers = @{
            'x-api-secret' = $API_SECRET
            'Cookie' = "next-auth.session-token=$SESSION_TOKEN"
            'User-Agent' = "ClusterTest-$i"
        }

        $response = Invoke-RestMethod `
            -Uri $authCheckUrl `
            -Method GET `
            -Headers $headers `
            -ErrorAction Stop

        $results += $response
        
        # 统计数据
        $serverIds[$response.serverId] = ($serverIds[$response.serverId] ?? 0) + 1
        $serverPorts[$response.serverPort] = ($serverPorts[$response.serverPort] ?? 0) + 1
        
        if ($response.isAuthenticated) { $authenticatedCount++ }
        if ($response.tokenValid) { $validTokenCount++ }
        
        $authStatus = if ($response.isAuthenticated) { "✅" } else { "❌" }
        $color = if ($response.isAuthenticated) { "Green" } else { "Red" }
        
        Write-Host "请求 $i`: 进程ID=$($response.serverId), 端口=$($response.serverPort), 认证=$authStatus" -ForegroundColor $color
        
    } catch {
        Write-Host "❌ 请求 $i 失败: $($_.Exception.Message)" -ForegroundColor Red
        $results += $null
    }
    
    # 等待间隔
    if ($i -lt $RequestCount) {
        Start-Sleep -Milliseconds $RequestInterval
    }
}

# 分析结果
Write-Host "`n📊 测试结果分析:" -ForegroundColor Cyan
Write-Host ("=" * 50) -ForegroundColor Cyan

$successfulRequests = ($results | Where-Object { $_ -ne $null }).Count
$uniqueServerIds = $serverIds.Keys.Count
$uniqueServerPorts = $serverPorts.Keys.Count

Write-Host "`n🎯 汇总统计:" -ForegroundColor Magenta
Write-Host "总请求数: $RequestCount" -ForegroundColor Blue
Write-Host "成功请求数: $successfulRequests" -ForegroundColor Green
Write-Host "不同进程数: $uniqueServerIds" -ForegroundColor $(if ($uniqueServerIds -gt 1) { "Green" } else { "Yellow" })
Write-Host "不同端口数: $uniqueServerPorts" -ForegroundColor Blue
Write-Host "认证成功数: $authenticatedCount" -ForegroundColor $(if ($authenticatedCount -eq $successfulRequests) { "Green" } else { "Red" })
Write-Host "Token有效数: $validTokenCount" -ForegroundColor $(if ($validTokenCount -eq $successfulRequests) { "Green" } else { "Red" })

Write-Host "`n🔍 进程分布:" -ForegroundColor Cyan
foreach ($serverId in $serverIds.Keys) {
    $count = $serverIds[$serverId]
    Write-Host "进程 $serverId`: $count 次请求" -ForegroundColor Blue
}

# 判断测试结果
Write-Host "`n🏆 测试结论:" -ForegroundColor Magenta

if ($uniqueServerIds -gt 1) {
    Write-Host "✅ 应用确实运行在多个进程上" -ForegroundColor Green
} else {
    Write-Host "⚠️  应用只运行在单个进程上" -ForegroundColor Yellow
}

if ($authenticatedCount -eq $successfulRequests -and $authenticatedCount -gt 0) {
    Write-Host "✅ Session/Cookie在所有进程间正常工作" -ForegroundColor Green
} else {
    Write-Host "❌ Session/Cookie存在问题" -ForegroundColor Red
}

if ($uniqueServerIds -gt 1 -and $authenticatedCount -eq $successfulRequests) {
    Write-Host "🎉 集群模式测试通过！" -ForegroundColor Green
} else {
    Write-Host "⚠️  集群模式可能存在问题" -ForegroundColor Yellow
}

# 显示详细的cookie信息（第一个成功的请求）
$firstSuccessfulResult = $results | Where-Object { $_ -ne $null } | Select-Object -First 1
if ($firstSuccessfulResult -and $firstSuccessfulResult.cookieInfo) {
    Write-Host "`n🍪 Cookie信息示例:" -ForegroundColor Cyan
    Write-Host "NextAuth Session Token: $($firstSuccessfulResult.cookieInfo.nextAuthSessionToken)" -ForegroundColor Yellow
    Write-Host "NextAuth CSRF Token: $($firstSuccessfulResult.cookieInfo.nextAuthCsrfToken)" -ForegroundColor Yellow
    Write-Host "所有Cookies数量: $($firstSuccessfulResult.cookieInfo.allCookies.Count)" -ForegroundColor Blue
}

Write-Host "`n✅ 测试完成！" -ForegroundColor Green
